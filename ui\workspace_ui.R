# 主工作区页面UI
# 用户打开项目后的工作界面

workspace_ui <- function() {
  fluidPage(
    # 自定义CSS样式
    tags$head(
      tags$style(HTML("
        .workspace-header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 15px 20px;
          margin-bottom: 0;
          border-radius: 0;
        }
        
        .workspace-title {
          font-size: 1.5em;
          font-weight: 300;
          margin: 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        
        .project-actions-header {
          display: flex;
          gap: 10px;
          align-items: center;
        }
        
        .header-btn {
          background: rgba(255,255,255,0.2);
          border: 1px solid rgba(255,255,255,0.3);
          color: white;
          padding: 8px 16px;
          border-radius: 5px;
          font-size: 0.9em;
          transition: all 0.3s ease;
        }
        
        .header-btn:hover {
          background: rgba(255,255,255,0.3);
          border-color: rgba(255,255,255,0.5);
          color: white;
        }
        
        .workspace-content {
          padding: 0;
        }
        
        .nav-tabs {
          background: #f8f9fa;
          border-bottom: 2px solid #dee2e6;
          padding: 0 20px;
        }
        
        .nav-tabs .nav-link {
          border: none;
          border-radius: 0;
          color: #666;
          font-weight: 500;
          padding: 15px 20px;
          margin-right: 5px;
          transition: all 0.3s ease;
        }
        
        .nav-tabs .nav-link:hover {
          border-color: transparent;
          color: #667eea;
          background: rgba(102, 126, 234, 0.1);
        }
        
        .nav-tabs .nav-link.active {
          color: #667eea;
          background: white;
          border-color: #dee2e6 #dee2e6 white;
          border-bottom: 2px solid #667eea;
        }
        
        .tab-content {
          background: white;
          min-height: calc(100vh - 120px);
        }
        
        .tab-pane {
          padding: 20px;
        }
        
        .module-card {
          background: white;
          border: 1px solid #dee2e6;
          border-radius: 8px;
          margin-bottom: 20px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .module-header {
          background: #f8f9fa;
          border-bottom: 1px solid #dee2e6;
          padding: 15px 20px;
          border-radius: 8px 8px 0 0;
        }
        
        .module-title {
          font-size: 1.2em;
          font-weight: 600;
          color: #333;
          margin: 0;
          display: flex;
          align-items: center;
          gap: 10px;
        }
        
        .module-content {
          padding: 20px;
        }
        
        .action-buttons {
          display: flex;
          gap: 10px;
          flex-wrap: wrap;
          margin-bottom: 20px;
        }
        
        .btn-custom {
          border-radius: 6px;
          font-weight: 500;
          padding: 10px 20px;
          transition: all 0.3s ease;
        }
        
        .btn-primary-custom {
          background: linear-gradient(45deg, #667eea, #764ba2);
          border: none;
          color: white;
        }
        
        .btn-primary-custom:hover {
          background: linear-gradient(45deg, #5a6fd8, #6a4190);
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .btn-success-custom {
          background: linear-gradient(45deg, #28a745, #20c997);
          border: none;
          color: white;
        }
        
        .btn-success-custom:hover {
          background: linear-gradient(45deg, #218838, #1ea080);
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .btn-warning-custom {
          background: linear-gradient(45deg, #ffc107, #fd7e14);
          border: none;
          color: white;
        }
        
        .btn-warning-custom:hover {
          background: linear-gradient(45deg, #e0a800, #e8690b);
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .btn-danger-custom {
          background: linear-gradient(45deg, #dc3545, #e83e8c);
          border: none;
          color: white;
        }

        .btn-info-custom {
          background: linear-gradient(45deg, #17a2b8, #6f42c1);
          border: none;
          color: white;
        }
        
        .btn-danger-custom:hover {
          background: linear-gradient(45deg, #c82333, #d91a72);
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .import-panel {
          padding: 15px;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          background: #f8f9fa;
          margin-bottom: 15px;
        }
        
        .info-display {
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 6px;
          padding: 15px;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
          line-height: 1.6;
          white-space: pre-wrap;
          max-height: 300px;
          overflow-y: auto;
        }
        
        .data-table-container {
          margin-top: 20px;
        }
        
        .table-actions {
          margin-bottom: 15px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 10px;
        }
        
        .table-actions-left {
          display: flex;
          gap: 10px;
          flex-wrap: wrap;
        }
        
        .table-actions-right {
          display: flex;
          gap: 10px;
          flex-wrap: wrap;
        }
        
        .status-badge {
          display: inline-block;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 0.8em;
          font-weight: 500;
        }
        
        .status-success {
          background: #d4edda;
          color: #155724;
        }
        
        .status-warning {
          background: #fff3cd;
          color: #856404;
        }
        
        .status-danger {
          background: #f8d7da;
          color: #721c24;
        }
        
        .status-info {
          background: #d1ecf1;
          color: #0c5460;
        }

        /* 导入状态显示样式 */
        .import-status-show {
          display: block !important;
        }

        .import-status-hide {
          display: none !important;
        }

        /* 折叠模块样式 */
        .collapsible-header {
          transition: background-color 0.3s ease;
        }

        .collapsible-header:hover {
          background-color: #e9ecef !important;
        }

        .collapse-icon {
          transition: transform 0.3s ease;
        }

        .module-content {
          transition: all 0.3s ease;
          overflow: hidden;
        }

        /* 质控监控专用样式 */
        .status-card {
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 8px;
          padding: 15px;
          text-align: center;
          margin-bottom: 15px;
          transition: all 0.3s ease;
        }

        .status-card:hover {
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          transform: translateY(-2px);
        }

        .status-title {
          font-size: 0.9em;
          color: #6c757d;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .status-value {
          font-size: 1.2em;
          font-weight: 600;
          color: #495057;
        }

        .results-summary {
          margin-bottom: 20px;
        }

        .summary-card {
          background: #ffffff;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 15px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .summary-title {
          color: #495057;
          font-size: 1.1em;
          margin-bottom: 15px;
          border-bottom: 2px solid #e9ecef;
          padding-bottom: 8px;
        }

        .chart-controls {
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 20px;
        }

        .chart-container {
          background: #ffffff;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 15px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .table-controls {
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 20px;
        }

        .cache-stats {
          background: #e3f2fd;
          border: 1px solid #bbdefb;
          border-radius: 8px;
          padding: 15px;
        }

        .cache-controls {
          background: #fff3e0;
          border: 1px solid #ffcc02;
          border-radius: 8px;
          padding: 15px;
        }

        /* 监控状态指示器 */
        .monitoring-status-running {
          color: #28a745;
          animation: pulse 2s infinite;
        }

        .monitoring-status-stopped {
          color: #6c757d;
        }

        .monitoring-status-failed {
          color: #dc3545;
        }

        .monitoring-status-completed {
          color: #007bff;
        }

        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }

        /* 进度条样式 */
        .progress-container {
          background: #e9ecef;
          border-radius: 4px;
          height: 8px;
          overflow: hidden;
          margin-top: 5px;
        }

        .progress-bar {
          background: linear-gradient(90deg, #007bff, #28a745);
          height: 100%;
          transition: width 0.3s ease;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .status-card {
            margin-bottom: 10px;
          }

          .chart-container {
            padding: 10px;
          }

          .summary-card {
            padding: 15px;
          }
        }
      ")),

      # JavaScript代码
      tags$script(HTML("
        // 显示导入状态
        Shiny.addCustomMessageHandler('showImportStatus', function(data) {
          var statusDiv = document.getElementById('import_status_display');
          var infoDiv = document.getElementById('import_progress_info');

          if (statusDiv && infoDiv) {
            infoDiv.textContent = '正在扫描文件...';
            statusDiv.classList.remove('import-status-hide');
            statusDiv.classList.add('import-status-show');
            statusDiv.style.display = 'block';
          }
        });

        // 隐藏导入状态
        Shiny.addCustomMessageHandler('hideImportStatus', function(data) {
          var statusDiv = document.getElementById('import_status_display');

          if (statusDiv) {
            statusDiv.classList.remove('import-status-show');
            statusDiv.classList.add('import-status-hide');
            setTimeout(function() {
              statusDiv.style.display = 'none';
            }, 300);
          }
        });

        // 更新导入进度
        Shiny.addCustomMessageHandler('updateImportProgress', function(data) {
          var infoDiv = document.getElementById('import_progress_info');

          if (infoDiv) {
            infoDiv.textContent = data.message || '处理中...';
          }
        });

        // 添加ESC键监听
        document.addEventListener('keydown', function(event) {
          if (event.key === 'Escape') {
            console.log('ESC键被按下');
            Shiny.setInputValue('escape_key', Math.random(), {priority: 'event'});
          }
        });

        // 添加Delete键监听
        document.addEventListener('keydown', function(event) {
          if (event.key === 'Delete') {
            console.log('Delete键被按下');
            Shiny.setInputValue('delete_key', Math.random(), {priority: 'event'});
          }
        });

        // 强制关闭所有模态框
        Shiny.addCustomMessageHandler('forceCloseModals', function(data) {
          console.log('强制关闭所有模态框');

          // 移除所有模态框
          var modals = document.querySelectorAll('.modal');
          modals.forEach(function(modal) {
            modal.style.display = 'none';
            modal.classList.remove('show', 'in');
          });

          // 移除模态框背景
          var backdrops = document.querySelectorAll('.modal-backdrop');
          backdrops.forEach(function(backdrop) {
            backdrop.remove();
          });

          // 恢复body的滚动
          document.body.classList.remove('modal-open');
          document.body.style.overflow = '';
          document.body.style.paddingRight = '';
        });

        // 模块折叠功能
        function toggleCollapse(contentId) {
          var content = document.getElementById(contentId);
          var icon = document.getElementById(contentId.replace('_content', '_icon'));

          if (content && icon) {
            var isCollapsed = content.style.display === 'none';

            if (isCollapsed) {
              // 展开
              content.style.display = 'block';
              // 更新图标
              icon.className = icon.className.replace('fa-chevron-right', 'fa-chevron-down');
              console.log('展开模块:', contentId);
            } else {
              // 折叠
              content.style.display = 'none';
              // 更新图标
              icon.className = icon.className.replace('fa-chevron-down', 'fa-chevron-right');
              console.log('折叠模块:', contentId);
            }
          } else {
            console.log('未找到元素:', contentId, content, icon);
          }
        }

        // 确保函数在全局作用域中可用
        window.toggleCollapse = toggleCollapse;

        // 监控离子自动计算功能

        // 自动计算分子离子峰质荷比（只支持常用离子类型）
        function calculateMolecularIonMZ(molecularWeight, ionType) {
          if (!molecularWeight || molecularWeight <= 0) return '';

          var mz = molecularWeight;
          switch(ionType) {
            case '[M+H]+':
              mz = molecularWeight + 1.007276;
              break;
            case '[M-H]-':
              mz = molecularWeight - 1.007276;
              break;
            default:
              mz = molecularWeight;
          }
          return mz.toFixed(6);
        }

        // 根据离子化模式获取对应的离子类型
        function getIonTypeFromMode(ionizationMode) {
          if (ionizationMode === 'positive') {
            return '[M+H]+';
          } else if (ionizationMode === 'negative') {
            return '[M-H]-';
          }
          return '[M+H]+'; // 默认
        }

        // 监听分子质量和离子化模式变化，自动计算质荷比
        function setupAutoCalculation() {
          // 监听分子质量输入变化
          $(document).on('input', '#molecular_weight', function() {
            updateMZCalculation();
          });

          // 监听离子化模式变化
          $(document).on('change', '#ionization_mode', function() {
            updateMZCalculation();
          });

          // 监听自动计算开关
          $(document).on('change', '#auto_calculate_mz', function() {
            updateMZCalculation();
          });
        }

        function updateMZCalculation() {
          var autoCalc = $('#auto_calculate_mz').is(':checked');
          var molecularWeight = parseFloat($('#molecular_weight').val());
          var ionizationMode = $('#ionization_mode').val();
          var ionType = getIonTypeFromMode(ionizationMode);

          if (autoCalc && molecularWeight && ionType) {
            var calculatedMZ = calculateMolecularIonMZ(molecularWeight, ionType);
            $('#ion_mz').val(calculatedMZ);
            // 触发Shiny的input事件，确保值被正确传递
            $('#ion_mz').trigger('change');
          }
        }

        // 页面加载完成后设置自动计算
        $(document).ready(function() {
          setupAutoCalculation();
        });



        // 全局函数
        window.calculateMolecularIonMZ = calculateMolecularIonMZ;
        window.setupAutoCalculation = setupAutoCalculation;
        window.updateMZCalculation = updateMZCalculation;
      "))
    ),
    
    # 工作区头部
    div(class = "workspace-header",
      div(class = "workspace-title",
        span(
          icon("flask"), 
          "实验室实时质控系统 - 工作区"
        ),
        div(class = "project-actions-header",
          span(id = "current_project_name", "项目名称"),
          actionButton("close_project_btn", "关闭项目", 
                      class = "header-btn",
                      icon = icon("times")),
          actionButton("project_settings_btn", "项目设置", 
                      class = "header-btn",
                      icon = icon("cog"))
        )
      )
    ),
    
    # 主要内容区域
    div(class = "workspace-content",
      tabsetPanel(
        id = "main_tabs",
        type = "tabs",
        
        # 项目信息标签页
        tabPanel("项目信息",
          value = "project_info",
          icon = icon("info-circle"),
          
          div(class = "module-card",
            div(class = "module-header",
              h3(class = "module-title",
                icon("folder-open"),
                "项目信息"
              )
            ),
            div(class = "module-content",
              div(class = "info-display",
                verbatimTextOutput("project_info")
              )
            )
          )
        ),
        
        # 数据管理标签页
        tabPanel("数据管理",
          value = "data_management",
          icon = icon("database"),

          # 数据导入模块
          div(class = "module-card",
            div(class = "module-header collapsible-header",
                onclick = "toggleCollapse('data_import_content')",
                style = "cursor: pointer; user-select: none;",
              h3(class = "module-title",
                icon("upload"),
                "数据导入",
                tags$span(class = "collapse-icon", style = "float: right; margin-top: 5px;",
                         icon("chevron-down", id = "data_import_icon"))
              )
            ),
            div(class = "module-content", id = "data_import_content",
              # 数据导入方式选择
              div(style = "margin-bottom: 20px;",
                h4("数据导入方式:", style = "margin-bottom: 15px; color: #333;"),

                # 导入方式选择器
                div(style = "margin-bottom: 15px;",
                  selectInput("import_method", "选择导入方式:",
                             choices = list(
                               "从文件夹导入" = "folder",
                               "从CSV表格导入" = "csv"
                             ),
                             selected = "folder",
                             width = "300px")
                ),

                # 文件夹导入界面
                conditionalPanel(
                  condition = "input.import_method == 'folder'",
                    div(style = "margin-top: 10px; display: flex; gap: 15px; flex-wrap: wrap;",
                      selectInput("folder_file_type", "文件类型:",
                                 choices = list(
                                   "所有支持的格式" = "all",
                                   "Thermo RAW (*.raw)" = "raw",
                                   "mzML (*.mzML)" = "mzML",
                                   "mzXML (*.mzXML)" = "mzXML",
                                   "MGF (*.mgf)" = "mgf"
                                 ),
                                 selected = "all", width = "200px"),
                      selectInput("folder_sample_type", "默认样本类型:",
                                 choices = list("QC样本" = "QC", "标准品" = "STD",
                                              "空白样本" = "BLANK", "实际样本" = "SAMPLE"),
                                 selected = "SAMPLE", width = "150px"),
                      checkboxInput("folder_include_subfolders", "包含子文件夹", value = TRUE)
                    ),
                  div(class = "import-panel",
                    div(style = "display: flex; gap: 15px; align-items: end; flex-wrap: wrap;",
                      div(style = "flex: 1; min-width: 300px;",
                        textInput("folder_import_path", "文件夹路径:",
                                 placeholder = "例如: D:/Data/LC-MS", width = "100%")
                      ),
                      div(style = "flex: 0 0 auto;",
                        actionButton("check_folder_path", "检查路径",
                                    class = "btn btn-info", icon = icon("search"))
                      ),
                      div(style = "flex: 0 0 auto;",
                        actionButton("start_folder_import", "开始导入",
                                    class = "btn btn-primary", icon = icon("upload"))
                      )
                    )
                  )
                ),

                # CSV导入界面
                conditionalPanel(
                  condition = "input.import_method == 'csv'",
                  div(class = "import-panel",
                    h5("📊 CSV表格导入", style = "color: #28a745; margin-bottom: 10px;"),
                    # 文件选择
                    div(style = "display: flex; gap: 15px; align-items: end; flex-wrap: wrap; margin-bottom: 15px;",
                      div(style = "flex: 1; min-width: 300px;",
                        fileInput("csv_import_file", "选择CSV文件:",
                                 accept = c(".csv", ".txt"), width = "100%")
                      ),
                      div(style = "flex: 0 0 auto;",
                        actionButton("preview_csv_file", "预览文件",
                                    class = "btn btn-info", icon = icon("eye"))
                      )
                    ),

                    # 列映射配置
                    conditionalPanel(
                      condition = "output.csv_preview_available",
                      div(style = "margin-bottom: 15px; padding: 15px; background: #e8f5e8; border-radius: 4px; border-left: 4px solid #28a745;",
                        h6("🔗 列映射配置", style = "color: #155724; margin-bottom: 10px;"),
                        div(style = "display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;",
                          div(
                            tags$label("文件名列:", style = "font-weight: bold; color: #155724;"),
                            selectInput("csv_filename_column", NULL,
                                       choices = NULL, width = "100%")
                          ),
                          div(
                            tags$label("文件路径列:", style = "font-weight: bold; color: #155724;"),
                            selectInput("csv_filepath_column", NULL,
                                       choices = NULL, width = "100%")
                          ),
                          div(
                            tags$label("样本类型列:", style = "font-weight: bold; color: #155724;"),
                            selectInput("csv_sampletype_column", NULL,
                                       choices = NULL, width = "100%")
                          ),
                          div(
                            tags$label("备注列:", style = "font-weight: bold; color: #155724;"),
                            selectInput("csv_notes_column", NULL,
                                       choices = NULL, width = "100%")
                          )
                        )
                      )
                    ),

                    # 导入按钮
                    div(style = "text-align: center; margin-bottom: 15px;",
                      actionButton("start_csv_import", "开始导入",
                                  class = "btn btn-success btn-lg", icon = icon("upload"),
                                  style = "min-width: 150px;")
                    ),

                    # 说明信息
                    div(style = "padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 0.9em;",
                      strong("💡 使用说明:"), br(),
                      "1. 选择CSV文件后点击'预览文件'", br(),
                      "2. 在列映射配置中选择对应的列", br(),
                      "3. 文件名和路径为必需列，样本类型和备注为可选列", br(),
                      "4. 支持导入不存在的文件（将标记为'待生成'）"
                    )
                  )
                )
              ),

              # 导入状态显示
              div(id = "import_status_display",
                style = "margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 6px; display: none;",
                h5("导入状态:", style = "margin-bottom: 10px;"),
                div(id = "import_progress_info", style = "font-family: monospace; white-space: pre-wrap;")
              )
            )
          ),

          # 数据转换模块
          div(class = "module-card",
            div(class = "module-header collapsible-header",
                onclick = "toggleCollapse('data_conversion_content')",
                style = "cursor: pointer; user-select: none;",
              h3(class = "module-title",
                icon("exchange-alt"),
                "数据转换",
                tags$span(class = "collapse-icon", style = "float: right; margin-top: 5px;",
                         icon("chevron-down", id = "data_conversion_icon"))
              )
            ),
            div(class = "module-content", id = "data_conversion_content",
              # 简化的转换流程概览
              div(class = "conversion-workflow-section", style = "margin-bottom: 25px;",
                h5("数据转换流程", style = "margin-bottom: 20px; color: #333; text-align: center;"),

                # 简化的流程指示器
                div(class = "simplified-workflow", style = "text-align: center; margin-bottom: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px;",
                  div(style = "font-size: 16px; color: #666; margin-bottom: 15px;",
                    "RAW 文件 → mzML 格式 → RDS 对象 → 数据库存储"
                  ),
                  div(style = "font-size: 18px; font-weight: bold; color: #007bff;",
                    textOutput("conversion_status", inline = TRUE)
                  )
                ),


              ),



              # 统一转换控制区域
              div(class = "unified-conversion-control", style = "margin-bottom: 25px;",
                # 主转换控制卡片
                div(class = "main-conversion-card",
                  style = "padding: 30px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; color: white; margin-bottom: 20px;",

                  # 标题区域
                  div(style = "text-align: center; margin-bottom: 25px;",
                    h4("完整数据转换流程", style = "margin: 0 0 10px 0; font-weight: 600;"),
                    p("RAW文件 → mzML格式 → RDS对象 → 数据库存储", style = "margin: 0; opacity: 0.9; font-size: 0.95em;")
                  ),

                  # 控制按钮和选项
                  fluidRow(
                    column(12,
                      div(style = "text-align: center;",
                        actionButton("start_conversion", "开始数据转换",
                                    class = "btn btn-light btn-lg",
                                    icon = icon("play-circle"),
                                    style = "padding: 12px 25px; font-weight: 600; font-size: 16px; margin-bottom: 15px;")
                      )
                    )
                  ),

                  # 转换选项
                  fluidRow(
                    column(4,
                      div(style = "text-align: center; color: white;",
                        checkboxInput("force_reconvert", "强制重新转换", value = FALSE,
                                     width = "100%")
                      )
                    ),
                    column(4,
                      div(style = "text-align: center; color: white;",
                        checkboxInput("enable_database_conversion", "实时数据库存储", value = TRUE,
                                     width = "100%")
                      )
                    ),
                    column(4,
                      div(style = "text-align: center; color: white;",
                        checkboxInput("auto_validate_database", "自动验证数据库", value = TRUE,
                                     width = "100%")
                      )
                    )
                  )
                ),

                # 辅助控制区域
                fluidRow(
                  column(6,
                    # 自动刷新统计
                    div(class = "control-card",
                      style = "padding: 20px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 12px; color: white; height: 160px; display: flex; flex-direction: column; justify-content: space-between;",
                      div(style = "display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;",
                        div(
                          h5("自动刷新统计", style = "margin: 0; font-weight: 600;"),
                          p("定期更新数据状态", style = "margin: 5px 0 0 0; opacity: 0.9; font-size: 0.9em;")
                        ),
                        div(style = "font-size: 1.5em;",
                          icon("sync-alt")
                        )
                      ),
                      div(
                        div(style = "margin: 0 0 8px 0; color: white;",
                          checkboxInput("enable_auto_refresh", "启用自动刷新", value = FALSE)
                        ),
                        numericInput("auto_refresh_interval", "间隔(分钟,1-60)",
                                    value = 1, min = 1, max = 60, step = 1, width = "100%")
                      )
                    )
                  ),
                  column(6,
                    # 自动转换新文件
                    div(class = "control-card",
                      style = "padding: 20px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 12px; color: white; height: 160px; display: flex; flex-direction: column; justify-content: space-between;",
                      div(style = "display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;",
                        div(
                          h5("自动转换新文件", style = "margin: 0; font-weight: 600;"),
                          p("检测并处理新增文件", style = "margin: 5px 0 0 0; opacity: 0.9; font-size: 0.9em;")
                        ),
                        div(style = "font-size: 1.5em;",
                          icon("magic")
                        )
                      ),
                      div(
                        div(style = "margin: 0 0 8px 0; color: white;",
                          checkboxInput("enable_auto_conversion", "启用自动转换", value = FALSE)
                        ),
                        numericInput("auto_conversion_check_interval", "检查间隔(秒,10-300)",
                                    value = 30, min = 10, max = 300, step = 10, width = "100%")
                      )
                    )
                  )
                )
              ),

              # 简化的转换进度显示
              div(class = "unified-progress-section", style = "margin-top: 25px;",
                h5("转换进度", style = "margin-bottom: 20px; color: #333; text-align: center;"),

                # 单一统一进度条
                div(style = "margin-bottom: 20px; padding: 20px; background: white; border: 1px solid #dee2e6; border-radius: 8px;",
                  div(style = "display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;",
                    h6("整体转换进度", style = "margin: 0; font-weight: bold; color: #333;"),
                    span(textOutput("processing_progress_percent", inline = TRUE), style = "font-weight: bold; color: #007bff; font-size: 18px;")
                  ),
                  div(class = "progress", style = "height: 25px; background-color: #e9ecef; border-radius: 12px;",
                    div(class = "progress-bar progress-bar-striped progress-bar-animated", id = "unified_progress_bar",
                        style = "width: 0%; background: linear-gradient(90deg, #007bff, #28a745); transition: width 0.5s ease; border-radius: 12px;")
                  ),
                  div(style = "margin-top: 15px; text-align: center; font-size: 14px; color: #666;",
                    textOutput("conversion_status", inline = TRUE)
                  )
                )
              ),

              # JavaScript for unified progress bar updates
              tags$script(HTML("
                // 统一进度条更新
                Shiny.addCustomMessageHandler('updateUnifiedProgressBar', function(data) {
                  $('#unified_progress_bar').css('width', data.percentage + '%');
                });

                Shiny.addCustomMessageHandler('resetUnifiedProgress', function(data) {
                  $('#unified_progress_bar').css('width', '0%');
                });

                // 转换状态更新
                Shiny.addCustomMessageHandler('updateConversionStatus', function(data) {
                  // 更新所有转换状态显示元素
                  $('[id*=\"conversion_status\"]').text(data.status);
                });

                // 自动更新进度条（基于文本输出）
                $(document).on('shiny:value', function(event) {
                  if (event.target.id === 'processing_progress_percent') {
                    var percentage = event.value.replace('%', '');
                    $('#unified_progress_bar').css('width', percentage + '%');
                  }
                });
              ")),

              # CSS样式优化
              tags$style(HTML("
                .control-card {
                  transition: transform 0.2s ease, box-shadow 0.2s ease;
                }
                .control-card:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                }
                .control-card .form-group {
                  margin-bottom: 0;
                }
                .control-card input[type='number'] {
                  background: rgba(255,255,255,0.2);
                  border: 1px solid rgba(255,255,255,0.3);
                  color: white;
                  border-radius: 4px;
                  padding: 6px 10px;
                }
                .control-card input[type='number']:focus {
                  background: rgba(255,255,255,0.3);
                  border-color: rgba(255,255,255,0.5);
                  box-shadow: 0 0 0 2px rgba(255,255,255,0.2);
                }
                .control-card input[type='number']::placeholder {
                  color: rgba(255,255,255,0.7);
                }
                .control-card .checkbox {
                  margin: 0;
                }
                .control-card .checkbox input[type='checkbox'] {
                  transform: scale(1.1);
                }
                @media (max-width: 768px) {
                  .control-card {
                    height: auto !important;
                    margin-bottom: 15px;
                    min-height: 160px;
                  }
                }
              "))
            )
          ),

          # 数据列表管理模块
          div(class = "module-card",
            div(class = "module-header",
              h3(class = "module-title",
                icon("table"),
                "数据列表管理"
              )
            ),
            div(class = "module-content",
              # 数据统计信息
              div(style = "margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;",
                div(style = "display: flex; gap: 30px; flex-wrap: wrap; align-items: center;",
                  div(style = "display: flex; align-items: center; gap: 8px;",
                    icon("file", style = "color: #667eea;"),
                    span("总文件数: "),
                    strong(textOutput("total_files_count", inline = TRUE))
                  ),
                  div(style = "display: flex; align-items: center; gap: 8px;",
                    icon("check-circle", style = "color: #28a745;"),
                    span("已处理: "),
                    strong(textOutput("processed_files_count", inline = TRUE))
                  ),
                  div(style = "display: flex; align-items: center; gap: 8px;",
                    icon("clock", style = "color: #ffc107;"),
                    span("处理中: "),
                    strong(textOutput("processing_files_count", inline = TRUE))
                  ),
                  div(style = "display: flex; align-items: center; gap: 8px;",
                    icon("hdd", style = "color: #6c757d;"),
                    span("总大小: "),
                    strong(textOutput("total_files_size", inline = TRUE))
                  )
                )
              ),

              # 操作按钮
              div(class = "table-actions",
                div(class = "table-actions-left",
                  actionButton("edit_selected_data", "编辑选中",
                              class = "btn-primary-custom btn-custom",
                              icon = icon("edit")),
                  actionButton("remove_selected_data", "删除选中",
                              class = "btn-danger-custom btn-custom",
                              icon = icon("trash")),
                  actionButton("refresh_data_list", "刷新列表",
                              class = "btn-success-custom btn-custom",
                              icon = icon("refresh"))
                ),
                div(class = "table-actions-right",
                  # 筛选选项 - 改为样本类型筛选
                  selectInput("sample_type_filter", "样本类型筛选:",
                            choices = list("全部" = "all", "QC样本" = "QC",
                                         "标准品" = "STD", "空白样本" = "BLANK",
                                         "实际样本" = "SAMPLE"),
                            selected = "all",
                            width = "150px"),
                  downloadButton("export_data_list", "导出列表",
                                class = "btn-warning-custom btn-custom")
                )
              ),

              # 数据表格
              div(class = "data-table-container",
                DT::dataTableOutput("data_list_table")
              )
            )
          )
        ),
        
        # 实验质控标签页  
        tabPanel("实验质控",
          value = "monitor_config",
          icon = icon("sliders-h"),
          
          # 同位素内标监控模块
          div(class = "module-card",
            div(class = "module-header collapsible-header",
                onclick = "toggleCollapse('eic_monitor_content')",
                style = "cursor: pointer; user-select: none;",
              h3(class = "module-title",
                icon("cogs"),
                "同位素内标监控",
                tags$span(class = "collapse-icon", style = "float: right; margin-top: 5px;",
                         icon("chevron-down", id = "eic_monitor_icon"))
              )
            ),
            div(class = "module-content", id = "eic_monitor_content",
              # MS2-MS1峰匹配子区域
              div(style = "margin: 20px 0; padding: 15px; background: #f0f8ff; border-radius: 8px; border: 1px solid #b0d4f1;",
                h5("MS2-MS1峰匹配", style = "margin-bottom: 15px; color: #666;"),
                div(style = "padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin-bottom: 15px;",
                  icon("info-circle", style = "color: #0c5460; margin-right: 8px;"),
                  span("将MS2扫描与对应的MS1峰进行匹配，建立前体离子与碎片离子的关联", style = "color: #0c5460;")
                ),

                # 匹配参数设置
                div(style = "margin-bottom: 15px;",
                  div(style = "display: inline-block; margin-right: 20px;",
                    numericInput("ms_matching_ppm_tolerance", "质量容差 (ppm):",
                               value = 20, min = 1, max = 100, step = 1, width = "120px")
                  ),
                  div(style = "display: inline-block; vertical-align: top; margin-top: 25px;",
                    actionButton("start_ms_matching", "开始匹配", icon = icon("link"),
                               class = "btn-primary"),
                    actionButton("view_ms_matching_stats", "查看统计", icon = icon("chart-bar"),
                               class = "btn-info", style = "margin-left: 10px;"),
                    actionButton("reset_ms_matching", "重置匹配", icon = icon("undo"),
                               class = "btn-warning", style = "margin-left: 10px;")
                  )
                ),

                # 状态显示
                div(style = "margin-bottom: 15px;",
                  span(style = "color: #6c757d; font-weight: bold;", textOutput("ms_matching_status", inline = TRUE)),
                  span(style = "margin-left: 20px; color: #28a745; font-weight: bold;", textOutput("ms_matching_progress", inline = TRUE))
                ),

                # 结果表格
                div(style = "margin-top: 15px;",
                  DT::dataTableOutput("ms_matching_result_table")
                )
              ),

              # 添加监控离子表单
              div(class = "module-header collapsible-header",
                  onclick = "toggleCollapse('add_monitor_ion_content')",
                  style = "cursor: pointer; user-select: none; margin-top: 10px;",
                h5(class = "module-title",
                  icon("plus-circle"),
                  "添加监控离子",
                  tags$span(class = "collapse-icon", style = "float: right; margin-top: 5px;",
                           icon("chevron-down", id = "add_monitor_ion_icon"))
                )
              ),
              div(class = "module-content", id = "add_monitor_ion_content",
                div(style = "margin-bottom: 20px; padding: 20px; background: white; border: 1px solid #dee2e6; border-radius: 8px;",
                  # 化合物基本信息
                  div(style = "display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;",
                    textInput("compound_name", "化合物名称:", placeholder = "例如: 咖啡因"),
                    numericInput("molecular_weight", "分子质量:", value = NULL, min = 0, step = 0.01),
                    numericInput("retention_time", "保留时间 (min):", value = NULL, min = 0, step = 0.1)
                  ),
                  # 离子信息
                  div(style = "display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;",
                    div(
                      selectInput("ionization_mode", "离子化模式:",
                                choices = list("正离子模式 [M+H]+" = "positive", "负离子模式 [M-H]-" = "negative"),
                                selected = "positive"),
                      div(style = "font-size: 0.8em; color: #666; margin-top: -10px;",
                          "正离子模式对应[M+H]+，负离子模式对应[M-H]-")
                    ),
                    numericInput("ion_mz", "质荷比 (m/z):", value = NULL, min = 0, step = 0.000001),
                    div(
                      checkboxInput("auto_calculate_mz", "自动计算质荷比", value = TRUE),
                      div(style = "font-size: 0.8em; color: #666; margin-top: -10px;",
                          "基于分子质量和离子化模式自动计算")
                    )
                  ),
                  # 备注和操作按钮
                  div(style = "display: grid; grid-template-columns: 1fr auto; gap: 15px; align-items: end;",
                    textAreaInput("ion_notes", "备注:", placeholder = "分子离子峰监控", rows = 2),
                    div(style = "display: flex; gap: 10px;",
                      actionButton("save_monitor_ion", "保存离子",
                                  class = "btn-success-custom btn-custom",
                                  icon = icon("save")),
                      actionButton("clear_form", "清空表单",
                                  class = "btn-secondary btn-custom",
                                  icon = icon("eraser"))
                    )
                  )
                )
              ),

              # 操作按钮
              div(class = "table-actions",
                div(class = "table-actions-left",
                  actionButton("edit_selected_ion", "编辑选中",
                              class = "btn-primary-custom btn-custom",
                              icon = icon("edit")),
                  actionButton("remove_selected_ions", "删除选中",
                              class = "btn-danger-custom btn-custom",
                              icon = icon("trash")),
                  actionButton("refresh_monitor_ions", "刷新列表",
                              class = "btn-success-custom btn-custom",
                              icon = icon("refresh"))
                ),
                div(class = "table-actions-right",
                  style = "display: flex; gap: 10px; align-items: center; justify-content: flex-end; flex-wrap: wrap;",
                  fileInput("import_yaml_file", "", accept = c(".yaml", ".yml"), width = "160px", buttonLabel = "选择YAML"),
                  actionButton("import_yaml_btn", "导入配置", class = "btn-info-custom btn-custom btn-sm", icon = icon("download")),
                  downloadButton("export_monitor_yaml", "导出YAML", class = "btn-warning-custom btn-custom", icon = icon("upload"))
                )
              ),

              # 数据表格
              div(class = "data-table-container",
                DT::dataTableOutput("monitor_ions_table")
              )
            )
          ),

          # 分布式内标监控模块
          div(class = "module-card",
            div(class = "module-header collapsible-header",
                onclick = "toggleCollapse('distributed_monitor_content')",
                style = "cursor: pointer; user-select: none;",
              h3(class = "module-title",
                icon("project-diagram"),
                "分布式内标监控",
                tags$span(style = "color: #f39c12; font-size: 0.8em; margin-left: 10px;", "(开发中)"),
                tags$span(class = "collapse-icon", style = "float: right; margin-top: 5px;",
                         icon("chevron-down", id = "distributed_monitor_icon"))
              )
            ),
            div(class = "module-content", id = "distributed_monitor_content",
              div(style = "text-align: center; padding: 40px 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #f39c12;",
                icon("tools", style = "font-size: 48px; color: #f39c12; margin-bottom: 20px;"),
                h4("功能开发中", style = "color: #2c3e50; margin-bottom: 15px;"),
                p("分布式内标监控功能正在重新开发中：", style = "color: #666; margin-bottom: 15px;"),
                div(style = "text-align: left; max-width: 400px; margin: 0 auto;",
                  p("由于同位素内标数量限制，无法全方位评估数据质量，因此需要自动识别QC样本中稳定出现的离子作为分布式内标监控的离子，全rtime范围，全mz范围进行质控", style = "color: #666; margin: 5px 0;")
                ),
                p("敬请期待后续版本更新", style = "color: #f39c12; font-weight: bold; margin-top: 20px;")
              )
            )
          ),

          # QC监控模块
          div(class = "module-card",
            div(class = "module-header collapsible-header",
                onclick = "toggleCollapse('qc_monitor_content')",
                style = "cursor: pointer; user-select: none;",
              h3(class = "module-title",
                icon("vial"),
                "QC监控",
                tags$span(style = "color: #f39c12; font-size: 0.8em; margin-left: 10px;", "(开发中)"),
                tags$span(class = "collapse-icon", style = "float: right; margin-top: 5px;",
                         icon("chevron-down", id = "qc_monitor_icon"))
              )
            ),
            div(class = "module-content", id = "qc_monitor_content",
              div(style = "text-align: center; padding: 40px 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #f39c12;",
                icon("tools", style = "font-size: 48px; color: #f39c12; margin-bottom: 20px;"),
                h4("功能开发中", style = "color: #2c3e50; margin-bottom: 15px;"),
                p("QC监控功能正在重新开发中，将提供：", style = "color: #666; margin-bottom: 15px;"),
                div(style = "text-align: left; max-width: 400px; margin: 0 auto;",
                  p("• 实时质量控制监控", style = "color: #666; margin: 5px 0;"),
                  p("• 多维度质量评估", style = "color: #666; margin: 5px 0;"),
                  p("• 智能异常预警系统", style = "color: #666; margin: 5px 0;"),
                  p("• 自动化QC报告生成", style = "color: #666; margin: 5px 0;")
                ),
                p("敬请期待后续版本更新", style = "color: #f39c12; font-weight: bold; margin-top: 20px;")
              )
            )
          ),

          # MS2监控模块
          div(class = "module-card",
            div(class = "module-header collapsible-header",
                onclick = "toggleCollapse('ms2_monitor_content')",
                style = "cursor: pointer; user-select: none;",
              h3(class = "module-title",
                icon("chart-bar"),
                "MS2监控",
                tags$span(style = "color: #f39c12; font-size: 0.8em; margin-left: 10px;", "(开发中)"),
                tags$span(class = "collapse-icon", style = "float: right; margin-top: 5px;",
                         icon("chevron-down", id = "ms2_monitor_icon"))
              )
            ),
            div(class = "module-content", id = "ms2_monitor_content",
              div(style = "text-align: center; padding: 40px 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #f39c12;",
                icon("tools", style = "font-size: 48px; color: #f39c12; margin-bottom: 20px;"),
                h4("功能开发中", style = "color: #2c3e50; margin-bottom: 15px;"),
                p("MS2监控功能正在重新开发中，将提供：", style = "color: #666; margin-bottom: 15px;"),
                div(style = "text-align: left; max-width: 400px; margin: 0 auto;",
                  p("• 二级质谱触发率监控", style = "color: #666; margin: 5px 0;"),
                  p("• Precursor精度分析", style = "color: #666; margin: 5px 0;"),
                  p("• 碎片离子强度分布", style = "color: #666; margin: 5px 0;"),
                  p("• 智能MS2质量评估", style = "color: #666; margin: 5px 0;")
                ),
                p("敬请期待后续版本更新", style = "color: #f39c12; font-weight: bold; margin-top: 20px;")
              )
            )
          ),

          # 文件诊断模块
          div(class = "module-card",
            div(class = "module-header collapsible-header",
                onclick = "toggleCollapse('file_diagnostics_content')",
                style = "cursor: pointer; user-select: none;",
              h3(class = "module-title",
                icon("file-medical"),
                "文件诊断",
                tags$span(style = "color: #f39c12; font-size: 0.8em; margin-left: 10px;", "(开发中)"),
                tags$span(class = "collapse-icon", style = "float: right; margin-top: 5px;",
                         icon("chevron-down", id = "file_diagnostics_icon"))
              )
            ),
            div(class = "module-content", id = "file_diagnostics_content",
              div(style = "text-align: center; padding: 40px 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #f39c12;",
                icon("tools", style = "font-size: 48px; color: #f39c12; margin-bottom: 20px;"),
                h4("功能开发中", style = "color: #2c3e50; margin-bottom: 15px;"),
                p("文件诊断功能正在重新开发中，将提供：", style = "color: #666; margin-bottom: 15px;"),
                div(style = "text-align: left; max-width: 400px; margin: 0 auto;",
                  p("• 文件完整性自动检测", style = "color: #666; margin: 5px 0;"),
                  p("• msconvert转换验证", style = "color: #666; margin: 5px 0;"),
                  p("• 扫描统计质量分析", style = "color: #666; margin: 5px 0;"),
                  p("• 智能诊断报告生成", style = "color: #666; margin: 5px 0;")
                ),
                p("敬请期待后续版本更新", style = "color: #f39c12; font-weight: bold; margin-top: 20px;")
              )
            )
          )
        )
      ), # 结束 tabsetPanel


    ) # 结束 div(class = "workspace-content")
  ) # 结束 div(class = "workspace-container")
}
