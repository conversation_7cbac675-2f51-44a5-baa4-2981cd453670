# MS2与MS1匹配算法
# 实现高性能的MS2扫描与对应MS1峰的匹配功能

# 加载必要的包
load_matching_packages <- function() {
  required_packages <- c("DBI", "RSQLite", "dplyr", "data.table")
  
  for (pkg in required_packages) {
    if (!requireNamespace(pkg, quietly = TRUE)) {
      stop(paste("Required package", pkg, "is not installed. Please install it first."))
    }
  }
  
  library(DBI)
  library(RSQLite)
  library(dplyr)
  library(data.table)
}

# 计算ppm误差
calculate_ppm_error <- function(observed_mz, theoretical_mz) {
  abs((observed_mz - theoretical_mz) / theoretical_mz * 1e6)
}

# 计算强度相似度得分（基于对数比值）
calculate_intensity_similarity <- function(intensity1, intensity2) {
  # 避免除零错误，添加小的常数
  epsilon <- 1e-6
  log_ratio <- abs(log10((intensity1 + epsilon) / (intensity2 + epsilon)))
  # 转换为相似度得分，log_ratio越小，相似度越高
  similarity <- exp(-log_ratio)
  return(similarity)
}

# 高性能MS2-MS1匹配算法
match_ms2_to_ms1_peaks <- function(db_path, ppm_tolerance = 20, batch_size = 1000, progress_callback = NULL) {
  cat("=== MS2与MS1峰匹配算法 ===\n")
  cat("数据库路径:", db_path, "\n")
  cat("质量容差:", ppm_tolerance, "ppm\n")
  cat("批处理大小:", batch_size, "\n")
  
  # 加载必要的包
  load_matching_packages()
  
  if (!file.exists(db_path)) {
    stop("数据库文件不存在:", db_path)
  }
  
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 启用外键约束和性能优化
    DBI::dbExecute(con, "PRAGMA foreign_keys = ON")
    DBI::dbExecute(con, "PRAGMA journal_mode = WAL")
    DBI::dbExecute(con, "PRAGMA synchronous = NORMAL")
    DBI::dbExecute(con, "PRAGMA cache_size = 50000")
    DBI::dbExecute(con, "PRAGMA temp_store = MEMORY")
    
    # 首先检查是否需要添加ms1_peak_id字段
    add_ms1_peak_id_column_if_needed(con)
    
    # 获取需要匹配的MS2数据总数
    total_ms2_query <- "
      SELECT COUNT(*) as total 
      FROM ms2_spectra_data 
      WHERE ms1_peak_id IS NULL AND prec_scan_num IS NOT NULL AND precursor_mz IS NOT NULL
    "
    total_ms2 <- DBI::dbGetQuery(con, total_ms2_query)$total
    
    if (total_ms2 == 0) {
      cat("没有需要匹配的MS2数据\n")
      return(list(success = TRUE, matched_count = 0, total_count = 0))
    }
    
    cat("需要匹配的MS2扫描数:", total_ms2, "\n")
    
    # 分批处理MS2数据
    matched_count <- 0
    processed_count <- 0
    
    # 获取所有需要匹配的MS2数据
    ms2_query <- "
      SELECT spectrum_id, file_id, prec_scan_num, precursor_mz, precursor_intensity
      FROM ms2_spectra_data 
      WHERE ms1_peak_id IS NULL AND prec_scan_num IS NOT NULL AND precursor_mz IS NOT NULL
      ORDER BY file_id, spectrum_id
    "
    
    ms2_data <- DBI::dbGetQuery(con, ms2_query)
    
    if (nrow(ms2_data) == 0) {
      cat("没有找到需要匹配的MS2数据\n")
      return(list(success = TRUE, matched_count = 0, total_count = 0))
    }
    
    # 转换为data.table以提高性能
    ms2_dt <- data.table::as.data.table(ms2_data)
    
    # 按file_id分组处理，提高查询效率
    file_ids <- unique(ms2_dt$file_id)
    
    for (file_id in file_ids) {
      cat("处理文件:", file_id, "\n")
      
      # 获取该文件的MS2数据
      file_ms2_dt <- ms2_dt[file_id == file_id]
      
      # 获取该文件的所有MS1数据（预加载以提高性能）
      ms1_query <- "
        SELECT s.spectrum_id, s.scan_index, p.peak_id, p.mz, p.intensity
        FROM ms1_spectra_data s
        JOIN ms1_peaks_data p ON s.spectrum_id = p.spectrum_id
        WHERE s.file_id = ?
        ORDER BY s.scan_index, p.intensity DESC
      "
      
      ms1_data <- DBI::dbGetQuery(con, ms1_query, list(file_id))
      
      if (nrow(ms1_data) == 0) {
        cat("  - 该文件没有MS1数据，跳过\n")
        next
      }
      
      ms1_dt <- data.table::as.data.table(ms1_data)
      data.table::setkey(ms1_dt, scan_index, mz)
      
      # 批量处理该文件的MS2数据
      batch_matches <- process_file_ms2_matching(file_ms2_dt, ms1_dt, ppm_tolerance)
      
      if (nrow(batch_matches) > 0) {
        # 批量更新数据库
        update_ms2_matches_batch(con, batch_matches)
        matched_count <- matched_count + nrow(batch_matches)
        cat("  - 匹配成功:", nrow(batch_matches), "个MS2扫描\n")
      }
      
      processed_count <- processed_count + nrow(file_ms2_dt)
      
      # 调用进度回调函数
      if (!is.null(progress_callback)) {
        progress <- processed_count / total_ms2 * 100
        progress_callback(progress, matched_count, processed_count)
      }
    }
    
    cat("匹配完成!\n")
    cat("总处理数:", processed_count, "\n")
    cat("匹配成功数:", matched_count, "\n")
    cat("匹配率:", round(matched_count / processed_count * 100, 2), "%\n")
    
    return(list(
      success = TRUE,
      matched_count = matched_count,
      total_count = processed_count,
      match_rate = matched_count / processed_count
    ))
    
  }, error = function(e) {
    cat("匹配过程中发生错误:", e$message, "\n")
    return(list(success = FALSE, error = e$message))
  }, finally = {
    DBI::dbDisconnect(con)
  })
}

# 检查并添加ms1_peak_id字段（如果需要）
add_ms1_peak_id_column_if_needed <- function(con) {
  # 检查ms1_peak_id字段是否存在
  table_info <- DBI::dbGetQuery(con, "PRAGMA table_info(ms2_spectra_data)")
  
  if (!"ms1_peak_id" %in% table_info$name) {
    cat("添加ms1_peak_id字段到ms2_spectra_data表...\n")
    
    # 添加字段
    DBI::dbExecute(con, "ALTER TABLE ms2_spectra_data ADD COLUMN ms1_peak_id INTEGER")
    
    # 添加外键约束（SQLite中需要重建表来添加外键约束）
    # 这里先添加索引，外键约束在表重建时处理
    DBI::dbExecute(con, "CREATE INDEX IF NOT EXISTS idx_ms2_spectra_ms1_peak_id ON ms2_spectra_data(ms1_peak_id)")
    
    cat("ms1_peak_id字段添加完成\n")
  }
}

# 处理单个文件的MS2匹配
process_file_ms2_matching <- function(ms2_dt, ms1_dt, ppm_tolerance) {
  matches <- data.table::data.table(
    spectrum_id = integer(),
    ms1_peak_id = integer(),
    stringsAsFactors = FALSE
  )
  
  for (i in 1:nrow(ms2_dt)) {
    ms2_row <- ms2_dt[i]
    
    # 查找对应的MS1扫描
    ms1_candidates <- ms1_dt[scan_index == ms2_row$prec_scan_num]
    
    if (nrow(ms1_candidates) == 0) {
      next
    }
    
    # 在候选MS1峰中查找m/z匹配的峰
    precursor_mz <- ms2_row$precursor_mz
    precursor_intensity <- ms2_row$precursor_intensity
    
    # 计算ppm误差并筛选
    ms1_candidates[, ppm_error := calculate_ppm_error(mz, precursor_mz)]
    mz_matches <- ms1_candidates[ppm_error <= ppm_tolerance]
    
    if (nrow(mz_matches) == 0) {
      next
    }
    
    # 如果有多个匹配，选择最佳匹配
    if (nrow(mz_matches) > 1) {
      # 计算综合得分：ppm误差权重0.6，强度相似度权重0.4
      if (!is.na(precursor_intensity) && precursor_intensity > 0) {
        mz_matches[, intensity_similarity := calculate_intensity_similarity(intensity, precursor_intensity)]
        mz_matches[, combined_score := (1 - ppm_error / ppm_tolerance) * 0.6 + intensity_similarity * 0.4]
      } else {
        # 如果没有前体强度信息，只基于ppm误差和峰强度
        mz_matches[, combined_score := (1 - ppm_error / ppm_tolerance) * 0.7 + (intensity / max(intensity)) * 0.3]
      }
      
      # 选择得分最高的峰
      best_match <- mz_matches[which.max(combined_score)]
    } else {
      best_match <- mz_matches[1]
    }
    
    # 添加匹配结果
    matches <- rbind(matches, data.table::data.table(
      spectrum_id = ms2_row$spectrum_id,
      ms1_peak_id = best_match$peak_id
    ))
  }
  
  return(matches)
}

# 批量更新MS2匹配结果
update_ms2_matches_batch <- function(con, matches) {
  if (nrow(matches) == 0) {
    return()
  }
  
  # 使用临时表进行批量更新
  temp_table_name <- paste0("temp_matches_", as.integer(Sys.time()))
  
  # 创建临时表
  DBI::dbWriteTable(con, temp_table_name, matches, temporary = TRUE)
  
  # 批量更新
  update_sql <- paste0("
    UPDATE ms2_spectra_data 
    SET ms1_peak_id = (
      SELECT ms1_peak_id 
      FROM ", temp_table_name, " 
      WHERE ", temp_table_name, ".spectrum_id = ms2_spectra_data.spectrum_id
    )
    WHERE spectrum_id IN (
      SELECT spectrum_id FROM ", temp_table_name, "
    )
  ")
  
  DBI::dbExecute(con, update_sql)
  
  # 删除临时表
  DBI::dbExecute(con, paste0("DROP TABLE ", temp_table_name))
}

# 获取匹配统计信息
get_matching_statistics <- function(db_path) {
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 总MS2数量
    total_ms2 <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms2_spectra_data")$count
    
    # 已匹配MS2数量
    matched_ms2 <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms2_spectra_data WHERE ms1_peak_id IS NOT NULL")$count
    
    # 按文件统计
    file_stats <- DBI::dbGetQuery(con, "
      SELECT 
        d.file_name,
        COUNT(m.spectrum_id) as total_ms2,
        SUM(CASE WHEN m.ms1_peak_id IS NOT NULL THEN 1 ELSE 0 END) as matched_ms2
      FROM data_files d
      LEFT JOIN ms2_spectra_data m ON d.file_id = m.file_id
      GROUP BY d.file_id, d.file_name
      HAVING total_ms2 > 0
      ORDER BY d.file_name
    ")
    
    return(list(
      total_ms2 = total_ms2,
      matched_ms2 = matched_ms2,
      match_rate = if (total_ms2 > 0) matched_ms2 / total_ms2 else 0,
      file_statistics = file_stats
    ))
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
}
