# MS2-MS1匹配失败分析脚本
# 分析为什么每个文件都有MS2扫描匹配失败

# 设置工作目录
setwd("d:/2025/团队管理/非靶V3.0/实验室实时质控")

# 加载必要的包
library(DBI)
library(RSQLite)

# 分析匹配失败原因
analyze_matching_failures <- function(db_path = NULL) {
  cat("=== MS2-MS1匹配失败原因分析 ===\n")
  
  # 如果没有指定数据库路径，尝试查找
  if (is.null(db_path)) {
    possible_paths <- c(
      "results/spectra.db",
      "test/QC3/results/spectra.db",
      "projects/QC3/results/spectra.db"
    )
    
    for (path in possible_paths) {
      if (file.exists(path)) {
        db_path <- path
        break
      }
    }
    
    if (is.null(db_path)) {
      cat("未找到数据库文件，请指定数据库路径\n")
      return(FALSE)
    }
  }
  
  cat("分析数据库:", db_path, "\n")
  
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 1. 按文件统计匹配失败情况
    cat("\n1. 按文件统计匹配失败情况:\n")
    file_failure_stats <- DBI::dbGetQuery(con, "
      SELECT 
        d.file_name,
        COUNT(m.spectrum_id) as total_ms2,
        SUM(CASE WHEN m.ms1_peak_id IS NOT NULL THEN 1 ELSE 0 END) as matched_ms2,
        SUM(CASE WHEN m.ms1_peak_id IS NULL THEN 1 ELSE 0 END) as failed_ms2
      FROM data_files d
      LEFT JOIN ms2_spectra_data m ON d.file_id = m.file_id
      GROUP BY d.file_id, d.file_name
      HAVING total_ms2 > 0
      ORDER BY d.file_name
    ")
    
    for (i in 1:nrow(file_failure_stats)) {
      fs <- file_failure_stats[i, ]
      cat("  ", basename(fs$file_name), ": 失败", fs$failed_ms2, "个，成功", fs$matched_ms2, "个\n")
    }
    
    # 2. 分析失败原因类型
    cat("\n2. 分析失败原因:\n")
    
    # 2.1 检查prec_scan_num为NULL的情况
    null_prec_scan <- DBI::dbGetQuery(con, "
      SELECT COUNT(*) as count 
      FROM ms2_spectra_data 
      WHERE ms1_peak_id IS NULL AND prec_scan_num IS NULL
    ")$count
    
    cat("  原因1 - prec_scan_num为NULL:", null_prec_scan, "个\n")
    
    # 2.2 检查precursor_mz为NULL的情况
    null_precursor_mz <- DBI::dbGetQuery(con, "
      SELECT COUNT(*) as count 
      FROM ms2_spectra_data 
      WHERE ms1_peak_id IS NULL AND precursor_mz IS NULL
    ")$count
    
    cat("  原因2 - precursor_mz为NULL:", null_precursor_mz, "个\n")
    
    # 2.3 检查找不到对应MS1扫描的情况
    no_ms1_scan <- DBI::dbGetQuery(con, "
      SELECT COUNT(*) as count
      FROM ms2_spectra_data m2
      WHERE m2.ms1_peak_id IS NULL 
        AND m2.prec_scan_num IS NOT NULL 
        AND m2.precursor_mz IS NOT NULL
        AND NOT EXISTS (
          SELECT 1 FROM ms1_spectra_data m1 
          WHERE m1.file_id = m2.file_id AND m1.scan_index = m2.prec_scan_num
        )
    ")$count
    
    cat("  原因3 - 找不到对应的MS1扫描:", no_ms1_scan, "个\n")
    
    # 2.4 检查MS1扫描存在但没有峰数据的情况
    no_ms1_peaks <- DBI::dbGetQuery(con, "
      SELECT COUNT(*) as count
      FROM ms2_spectra_data m2
      WHERE m2.ms1_peak_id IS NULL 
        AND m2.prec_scan_num IS NOT NULL 
        AND m2.precursor_mz IS NOT NULL
        AND EXISTS (
          SELECT 1 FROM ms1_spectra_data m1 
          WHERE m1.file_id = m2.file_id AND m1.scan_index = m2.prec_scan_num
        )
        AND NOT EXISTS (
          SELECT 1 FROM ms1_spectra_data m1
          JOIN ms1_peaks_data p ON m1.spectrum_id = p.spectrum_id
          WHERE m1.file_id = m2.file_id AND m1.scan_index = m2.prec_scan_num
        )
    ")$count
    
    cat("  原因4 - MS1扫描存在但无峰数据:", no_ms1_peaks, "个\n")
    
    # 2.5 检查质量匹配失败的情况（ppm误差过大）
    ppm_mismatch <- DBI::dbGetQuery(con, "
      SELECT COUNT(*) as count
      FROM ms2_spectra_data m2
      WHERE m2.ms1_peak_id IS NULL 
        AND m2.prec_scan_num IS NOT NULL 
        AND m2.precursor_mz IS NOT NULL
        AND EXISTS (
          SELECT 1 FROM ms1_spectra_data m1
          JOIN ms1_peaks_data p ON m1.spectrum_id = p.spectrum_id
          WHERE m1.file_id = m2.file_id 
            AND m1.scan_index = m2.prec_scan_num
            AND ABS(p.mz - m2.precursor_mz) / m2.precursor_mz * 1e6 > 20
        )
    ")$count
    
    cat("  原因5 - 质量匹配失败(ppm>20):", ppm_mismatch, "个\n")
    
    # 3. 详细分析每个文件的失败案例
    cat("\n3. 详细分析每个文件的失败案例:\n")
    
    files <- DBI::dbGetQuery(con, "SELECT file_id, file_name FROM data_files")
    
    for (i in 1:nrow(files)) {
      file_info <- files[i, ]
      cat("  文件:", basename(file_info$file_name), "\n")
      
      # 获取该文件的失败MS2扫描
      failed_ms2 <- DBI::dbGetQuery(con, "
        SELECT spectrum_id, scan_index, rtime, precursor_mz, prec_scan_num
        FROM ms2_spectra_data 
        WHERE file_id = ? AND ms1_peak_id IS NULL
        ORDER BY rtime
        LIMIT 5
      ", list(file_info$file_id))
      
      if (nrow(failed_ms2) > 0) {
        cat("    前", min(5, nrow(failed_ms2)), "个失败案例:\n")
        
        for (j in 1:nrow(failed_ms2)) {
          ms2 <- failed_ms2[j, ]
          cat("      MS2扫描", ms2$spectrum_id, ":")
          
          # 检查具体失败原因
          if (is.na(ms2$prec_scan_num)) {
            cat(" prec_scan_num为NULL\n")
          } else if (is.na(ms2$precursor_mz)) {
            cat(" precursor_mz为NULL\n")
          } else {
            # 检查是否存在对应的MS1扫描
            ms1_exists <- DBI::dbGetQuery(con, "
              SELECT COUNT(*) as count 
              FROM ms1_spectra_data 
              WHERE file_id = ? AND scan_index = ?
            ", list(file_info$file_id, ms2$prec_scan_num))$count
            
            if (ms1_exists == 0) {
              cat(" 找不到MS1扫描(scan_index=", ms2$prec_scan_num, ")\n")
            } else {
              # 检查是否有峰数据
              peak_count <- DBI::dbGetQuery(con, "
                SELECT COUNT(*) as count
                FROM ms1_spectra_data m1
                JOIN ms1_peaks_data p ON m1.spectrum_id = p.spectrum_id
                WHERE m1.file_id = ? AND m1.scan_index = ?
              ", list(file_info$file_id, ms2$prec_scan_num))$count
              
              if (peak_count == 0) {
                cat(" MS1扫描无峰数据\n")
              } else {
                # 检查质量匹配
                matching_peaks <- DBI::dbGetQuery(con, "
                  SELECT COUNT(*) as count
                  FROM ms1_spectra_data m1
                  JOIN ms1_peaks_data p ON m1.spectrum_id = p.spectrum_id
                  WHERE m1.file_id = ? 
                    AND m1.scan_index = ?
                    AND ABS(p.mz - ?) / ? * 1e6 <= 20
                ", list(file_info$file_id, ms2$prec_scan_num, ms2$precursor_mz, ms2$precursor_mz))$count
                
                if (matching_peaks == 0) {
                  cat(" 无质量匹配的峰(ppm>20)\n")
                } else {
                  cat(" 未知原因(应该能匹配)\n")
                }
              }
            }
          }
        }
      } else {
        cat("    该文件所有MS2都匹配成功\n")
      }
    }
    
    # 4. 检查扫描索引的连续性
    cat("\n4. 检查扫描索引连续性:\n")
    
    for (i in 1:nrow(files)) {
      file_info <- files[i, ]
      
      # 获取MS1和MS2的扫描索引范围
      ms1_range <- DBI::dbGetQuery(con, "
        SELECT MIN(scan_index) as min_scan, MAX(scan_index) as max_scan, COUNT(*) as count
        FROM ms1_spectra_data WHERE file_id = ?
      ", list(file_info$file_id))
      
      ms2_prec_range <- DBI::dbGetQuery(con, "
        SELECT MIN(prec_scan_num) as min_prec, MAX(prec_scan_num) as max_prec, COUNT(*) as count
        FROM ms2_spectra_data WHERE file_id = ? AND prec_scan_num IS NOT NULL
      ", list(file_info$file_id))
      
      cat("  ", basename(file_info$file_name), ":\n")
      cat("    MS1扫描索引范围:", ms1_range$min_scan, "-", ms1_range$max_scan, "(", ms1_range$count, "个)\n")
      if (nrow(ms2_prec_range) > 0 && !is.na(ms2_prec_range$min_prec)) {
        cat("    MS2前体扫描范围:", ms2_prec_range$min_prec, "-", ms2_prec_range$max_prec, "(", ms2_prec_range$count, "个)\n")
        
        # 检查是否有MS2的prec_scan_num超出MS1范围
        if (ms2_prec_range$min_prec < ms1_range$min_scan || ms2_prec_range$max_prec > ms1_range$max_scan) {
          cat("    ⚠️  MS2前体扫描索引超出MS1范围\n")
        }
      }
    }
    
    cat("\n=== 分析完成 ===\n")
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
}

# 运行分析
if (interactive()) {
  cat("开始MS2-MS1匹配失败原因分析...\n")
  analyze_matching_failures()
} else {
  cat("匹配失败分析脚本已加载\n")
  cat("使用 analyze_matching_failures() 运行分析\n")
}
