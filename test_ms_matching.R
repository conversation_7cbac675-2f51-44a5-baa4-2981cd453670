# MS2-MS1峰匹配功能测试脚本

# 设置工作目录
setwd("d:/2025/团队管理/非靶V3.0/实验室实时质控")

# 加载必要的脚本
source("utils/database_upgrade.R")
source("utils/ms_matching_algorithm.R")

# 测试函数
test_ms_matching <- function(db_path = "test/a1/results/spectra1.db") {
  cat("=== MS2-MS1峰匹配功能测试 ===\n")
  
  # 如果没有指定数据库路径，尝试查找
  if (is.null(db_path)) {
    # 查找可能的数据库路径
    possible_paths <- c(
      "data/spectra.db",
      "test/QC3/data/spectra.db",
      "projects/QC3/data/spectra.db"
    )
    
    for (path in possible_paths) {
      if (file.exists(path)) {
        db_path <- path
        break
      }
    }
    
    if (is.null(db_path)) {
      cat("未找到数据库文件，请指定数据库路径\n")
      return(FALSE)
    }
  }
  
  cat("使用数据库:", db_path, "\n")
  
  # 1. 检查数据库版本并升级
  cat("\n1. 检查数据库版本...\n")
  current_version <- get_database_version(db_path)
  cat("当前版本:", current_version %||% "未知", "\n")
  
  if (is.null(current_version) || current_version < "1.1") {
    cat("需要升级数据库以支持MS2-MS1匹配功能\n")
    upgrade_success <- safe_upgrade_database(db_path, "1.1")
    if (!upgrade_success) {
      cat("数据库升级失败\n")
      return(FALSE)
    }
  }
  
  # 2. 检查数据库内容
  cat("\n2. 检查数据库内容...\n")
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 检查表结构
    ms2_table_info <- DBI::dbGetQuery(con, "PRAGMA table_info(ms2_spectra_data)")
    if (!"ms1_peak_id" %in% ms2_table_info$name) {
      cat("错误：ms2_spectra_data表中缺少ms1_peak_id字段\n")
      return(FALSE)
    }
    
    # 统计数据
    ms1_count <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms1_spectra_data")$count
    ms2_count <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms2_spectra_data")$count
    ms1_peaks_count <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms1_peaks_data")$count
    
    cat("MS1扫描数:", ms1_count, "\n")
    cat("MS2扫描数:", ms2_count, "\n")
    cat("MS1峰数:", ms1_peaks_count, "\n")
    
    if (ms2_count == 0) {
      cat("数据库中没有MS2数据，无法进行匹配测试\n")
      return(FALSE)
    }
    
    if (ms1_peaks_count == 0) {
      cat("数据库中没有MS1峰数据，无法进行匹配测试\n")
      return(FALSE)
    }
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
  
  # 3. 执行匹配测试
  cat("\n3. 执行MS2-MS1匹配...\n")
  
  # 定义进度回调函数
  progress_callback <- function(progress, matched_count, processed_count) {
    cat("进度:", round(progress, 1), "%, 已匹配:", matched_count, ", 已处理:", processed_count, "\n")
  }
  
  # 执行匹配
  result <- match_ms2_to_ms1_peaks(
    db_path = db_path,
    ppm_tolerance = 20,
    batch_size = 500,
    progress_callback = progress_callback
  )
  
  if (!result$success) {
    cat("匹配失败:", result$error, "\n")
    return(FALSE)
  }
  
  # 4. 验证匹配结果
  cat("\n4. 验证匹配结果...\n")
  stats <- get_matching_statistics(db_path)
  
  cat("匹配统计:\n")
  cat("  总MS2扫描数:", stats$total_ms2, "\n")
  cat("  已匹配数:", stats$matched_ms2, "\n")
  cat("  匹配率:", round(stats$match_rate * 100, 2), "%\n")
  
  if (nrow(stats$file_statistics) > 0) {
    cat("\n按文件统计:\n")
    for (i in 1:nrow(stats$file_statistics)) {
      file_stat <- stats$file_statistics[i, ]
      cat("  ", basename(file_stat$file_name), ":", 
          file_stat$matched_ms2, "/", file_stat$total_ms2, 
          " (", round(file_stat$matched_ms2 / file_stat$total_ms2 * 100, 1), "%)\n")
    }
  }
  
  # 5. 验证匹配质量
  cat("\n5. 验证匹配质量...\n")
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 检查匹配的质量
    quality_check_sql <- "
      SELECT 
        COUNT(*) as total_matches,
        AVG(ABS(m.precursor_mz - p.mz) / m.precursor_mz * 1e6) as avg_ppm_error,
        MAX(ABS(m.precursor_mz - p.mz) / m.precursor_mz * 1e6) as max_ppm_error,
        MIN(ABS(m.precursor_mz - p.mz) / m.precursor_mz * 1e6) as min_ppm_error
      FROM ms2_spectra_data m
      JOIN ms1_peaks_data p ON m.ms1_peak_id = p.peak_id
      WHERE m.ms1_peak_id IS NOT NULL AND m.precursor_mz IS NOT NULL
    "
    
    quality_stats <- DBI::dbGetQuery(con, quality_check_sql)
    
    if (nrow(quality_stats) > 0 && quality_stats$total_matches > 0) {
      cat("匹配质量统计:\n")
      cat("  匹配数量:", quality_stats$total_matches, "\n")
      cat("  平均ppm误差:", round(quality_stats$avg_ppm_error, 2), "\n")
      cat("  最大ppm误差:", round(quality_stats$max_ppm_error, 2), "\n")
      cat("  最小ppm误差:", round(quality_stats$min_ppm_error, 2), "\n")
      
      # 检查是否有异常的匹配
      if (quality_stats$max_ppm_error > 25) {
        cat("警告：发现ppm误差超过25的匹配，请检查匹配算法\n")
      }
    }
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
  
  cat("\n=== 测试完成 ===\n")
  cat("MS2-MS1峰匹配功能测试通过\n")
  
  return(TRUE)
}

# 运行测试
if (interactive()) {
  cat("开始MS2-MS1峰匹配功能测试...\n")
  test_result <- test_ms_matching()
  
  if (test_result) {
    cat("所有测试通过！\n")
  } else {
    cat("测试失败，请检查错误信息\n")
  }
} else {
  cat("测试脚本已加载，使用 test_ms_matching() 函数运行测试\n")
}
