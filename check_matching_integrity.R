# MS2-MS1匹配完整性检查脚本
# 用于检查和修复1对多匹配问题

# 设置工作目录
setwd("d:/2025/团队管理/非靶V3.0/实验室实时质控")

# 加载必要的包
library(DBI)
library(RSQLite)

# 检查匹配完整性
check_matching_integrity <- function(db_path = NULL) {
  cat("=== MS2-MS1匹配完整性检查 ===\n")
  
  # 如果没有指定数据库路径，尝试查找
  if (is.null(db_path)) {
    possible_paths <- c(
      "results/spectra.db",
      "test/QC3/results/spectra.db",
      "projects/QC3/results/spectra.db"
    )
    
    for (path in possible_paths) {
      if (file.exists(path)) {
        db_path <- path
        break
      }
    }
    
    if (is.null(db_path)) {
      cat("未找到数据库文件，请指定数据库路径\n")
      return(FALSE)
    }
  }
  
  cat("检查数据库:", db_path, "\n")
  
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 1. 基本统计
    cat("\n1. 基本统计信息:\n")
    total_ms2 <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms2_spectra_data")$count
    matched_ms2 <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms2_spectra_data WHERE ms1_peak_id IS NOT NULL")$count
    total_ms1_peaks <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms1_peaks_data")$count
    
    cat("  总MS2扫描数:", total_ms2, "\n")
    cat("  已匹配MS2数:", matched_ms2, "\n")
    cat("  总MS1峰数:", total_ms1_peaks, "\n")
    cat("  匹配率:", round(matched_ms2 / total_ms2 * 100, 2), "%\n")
    
    # 2. 检查重复匹配（多个MS2指向同一个MS1峰）
    cat("\n2. 检查重复匹配:\n")
    duplicate_matches <- DBI::dbGetQuery(con, "
      SELECT 
        ms1_peak_id,
        COUNT(*) as ms2_count,
        GROUP_CONCAT(spectrum_id) as ms2_spectrum_ids
      FROM ms2_spectra_data 
      WHERE ms1_peak_id IS NOT NULL 
      GROUP BY ms1_peak_id 
      HAVING COUNT(*) > 1
      ORDER BY ms2_count DESC
    ")
    
    if (nrow(duplicate_matches) > 0) {
      cat("  发现", nrow(duplicate_matches), "个MS1峰被多个MS2扫描匹配\n")
      cat("  最多的一个MS1峰被", max(duplicate_matches$ms2_count), "个MS2扫描匹配\n")
      
      # 显示前5个重复匹配的详细信息
      cat("  前5个重复匹配详情:\n")
      for (i in 1:min(5, nrow(duplicate_matches))) {
        dup <- duplicate_matches[i, ]
        cat("    MS1峰ID", dup$ms1_peak_id, "被", dup$ms2_count, "个MS2扫描匹配\n")
      }
    } else {
      cat("  未发现重复匹配，每个MS1峰最多只被一个MS2扫描匹配\n")
    }
    
    # 3. 检查匹配质量
    cat("\n3. 检查匹配质量:\n")
    quality_stats <- DBI::dbGetQuery(con, "
      SELECT 
        COUNT(*) as total_matches,
        AVG(ABS(m.precursor_mz - p.mz) / m.precursor_mz * 1e6) as avg_ppm_error,
        MAX(ABS(m.precursor_mz - p.mz) / m.precursor_mz * 1e6) as max_ppm_error,
        MIN(ABS(m.precursor_mz - p.mz) / m.precursor_mz * 1e6) as min_ppm_error,
        COUNT(CASE WHEN ABS(m.precursor_mz - p.mz) / m.precursor_mz * 1e6 > 20 THEN 1 END) as over_20ppm
      FROM ms2_spectra_data m
      JOIN ms1_peaks_data p ON m.ms1_peak_id = p.peak_id
      WHERE m.ms1_peak_id IS NOT NULL AND m.precursor_mz IS NOT NULL
    ")
    
    if (nrow(quality_stats) > 0 && quality_stats$total_matches > 0) {
      cat("  匹配质量统计:\n")
      cat("    总匹配数:", quality_stats$total_matches, "\n")
      cat("    平均ppm误差:", round(quality_stats$avg_ppm_error, 2), "\n")
      cat("    最大ppm误差:", round(quality_stats$max_ppm_error, 2), "\n")
      cat("    最小ppm误差:", round(quality_stats$min_ppm_error, 2), "\n")
      cat("    超过20ppm的匹配数:", quality_stats$over_20ppm, "\n")
      
      if (quality_stats$over_20ppm > 0) {
        cat("  警告：发现", quality_stats$over_20ppm, "个ppm误差超过20的匹配\n")
      }
    }
    
    # 4. 按文件检查匹配分布
    cat("\n4. 按文件检查匹配分布:\n")
    file_stats <- DBI::dbGetQuery(con, "
      SELECT 
        d.file_name,
        COUNT(m.spectrum_id) as total_ms2,
        SUM(CASE WHEN m.ms1_peak_id IS NOT NULL THEN 1 ELSE 0 END) as matched_ms2,
        ROUND(SUM(CASE WHEN m.ms1_peak_id IS NOT NULL THEN 1 ELSE 0 END) * 100.0 / COUNT(m.spectrum_id), 2) as match_rate
      FROM data_files d
      LEFT JOIN ms2_spectra_data m ON d.file_id = m.file_id
      GROUP BY d.file_id, d.file_name
      HAVING total_ms2 > 0
      ORDER BY d.file_name
    ")
    
    if (nrow(file_stats) > 0) {
      cat("  文件匹配统计:\n")
      for (i in 1:nrow(file_stats)) {
        fs <- file_stats[i, ]
        cat("    ", basename(fs$file_name), ":", fs$matched_ms2, "/", fs$total_ms2, 
            " (", fs$match_rate, "%)\n")
      }
    }
    
    # 5. 检查异常匹配
    cat("\n5. 检查异常匹配:\n")
    
    # 检查跨文件匹配（MS2和MS1来自不同文件）
    cross_file_matches <- DBI::dbGetQuery(con, "
      SELECT COUNT(*) as count
      FROM ms2_spectra_data m2
      JOIN ms1_peaks_data p ON m2.ms1_peak_id = p.peak_id
      JOIN ms1_spectra_data m1 ON p.spectrum_id = m1.spectrum_id
      WHERE m2.file_id != m1.file_id AND m2.ms1_peak_id IS NOT NULL
    ")
    
    if (cross_file_matches$count > 0) {
      cat("  警告：发现", cross_file_matches$count, "个跨文件匹配（MS2和MS1来自不同文件）\n")
    } else {
      cat("  未发现跨文件匹配，所有匹配都在同一文件内\n")
    }
    
    # 6. 总结
    cat("\n6. 检查总结:\n")
    issues_found <- 0
    
    if (nrow(duplicate_matches) > 0) {
      cat("  ❌ 发现重复匹配问题\n")
      issues_found <- issues_found + 1
    } else {
      cat("  ✅ 无重复匹配问题\n")
    }
    
    if (quality_stats$over_20ppm > 0) {
      cat("  ❌ 发现质量问题（ppm误差过大）\n")
      issues_found <- issues_found + 1
    } else {
      cat("  ✅ 匹配质量良好\n")
    }
    
    if (cross_file_matches$count > 0) {
      cat("  ❌ 发现跨文件匹配问题\n")
      issues_found <- issues_found + 1
    } else {
      cat("  ✅ 无跨文件匹配问题\n")
    }
    
    if (issues_found == 0) {
      cat("  🎉 所有检查通过，匹配结果完整性良好！\n")
    } else {
      cat("  ⚠️  发现", issues_found, "个问题，建议重新进行匹配\n")
    }
    
    return(list(
      total_ms2 = total_ms2,
      matched_ms2 = matched_ms2,
      duplicate_matches = duplicate_matches,
      quality_stats = quality_stats,
      file_stats = file_stats,
      cross_file_matches = cross_file_matches$count,
      issues_found = issues_found
    ))
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
}

# 清理重复匹配
clean_duplicate_matches <- function(db_path = NULL) {
  cat("=== 清理重复匹配 ===\n")
  
  if (is.null(db_path)) {
    possible_paths <- c(
      "results/spectra.db",
      "test/QC3/results/spectra.db", 
      "projects/QC3/results/spectra.db"
    )
    
    for (path in possible_paths) {
      if (file.exists(path)) {
        db_path <- path
        break
      }
    }
  }
  
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 重置所有匹配结果
    cat("重置所有MS2-MS1匹配结果...\n")
    reset_count <- DBI::dbExecute(con, "UPDATE ms2_spectra_data SET ms1_peak_id = NULL WHERE ms1_peak_id IS NOT NULL")
    cat("已重置", reset_count, "个匹配结果\n")
    
    cat("清理完成，建议重新运行匹配算法\n")
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
}

# 运行检查
if (interactive()) {
  cat("开始MS2-MS1匹配完整性检查...\n")
  result <- check_matching_integrity()
  
  if (result$issues_found > 0) {
    cat("\n发现问题，是否要清理重复匹配？(y/n): ")
    response <- readline()
    if (tolower(response) == "y") {
      clean_duplicate_matches()
    }
  }
} else {
  cat("完整性检查脚本已加载\n")
  cat("使用 check_matching_integrity() 检查匹配完整性\n")
  cat("使用 clean_duplicate_matches() 清理重复匹配\n")
}
